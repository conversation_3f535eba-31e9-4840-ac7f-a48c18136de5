<script>
  import { enhance } from "$app/forms";
  import { goto } from "$app/navigation";
  import { invalidateAll } from "$app/navigation";
  import { timeAgo } from '$lib/utils/dateUtils';

  export let data;
  export let form;

  // device is a plain object
  let device = data.device ? data.device : null;

  // Form fields
  let ip = device?.ip || "";
  let nickname = device?.nickname || "";
  let vpnConf = device?.vpn_conf || "";
  let msgConf = device?.msg_conf || "";
  let phoneConf = device?.phone_conf || "";
  let teamId = device?.team_id || "";
  let role = device?.role || "Single";
  let lang = device?.lang || "";

  let validationError = null;

  $: if (device) {
    ip = device.ip || "";
    nickname = device.nickname || "";
    vpnConf = device.vpn_conf || "";
    msgConf = device.msg_conf || "";
    phoneConf = device.phone_conf || "";
    teamId = device.team_id || "";
    lang = device.lang || "";
    role = device.role || "Single";
    validationError = null; // Clear validation errors when device data changes
  }

  function formatDate(dateString) {
    if (!dateString) return "-";
    try {
      const date = new Date(dateString);
      return date.toLocaleString(undefined, {
        year: "numeric",
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "Invalid date";
    }
  }


  // Client-side validation function
  function validateForm() {
    if (!teamId.trim()) {
      return "Team ID is required";
    }
    return null;
  }

  // Enhanced form submission with validation
  function handleEnhancedSubmit() {
    return async ({ formData, cancel }) => {
      const error = validateForm();
      if (error) {
        validationError = error;
        cancel();
        return;
      }

      validationError = null;

      // Return a function to handle the result after form submission
      return async ({ result }) => {
        if (result.type === 'success') {
          // Invalidate all data to refresh the page
          await invalidateAll();
        } else if (result.type === 'redirect') {
          // Handle redirects with data invalidation
          await invalidateAll();
          goto(result.location);
        }
      };
    };
  }
</script>

<svelte:head>
  <title>Phantom | Device Details</title>
</svelte:head>

<div class="admin-container">
  {#if device}
    <div class="admin-card">
      <h1 class="admin-title">{device.ip} - Device Details</h1>

      <hr
        style="margin-top: 0.5rem; margin-bottom: 1rem; border-color: #333;"
      />

      {#if validationError}
        <div class="error-message">
          <span>{validationError}</span>
        </div>
      {/if}

      {#if form?.error}
        <div class="error-message">
          <span>{form.error}</span>
        </div>
      {/if}

      <div class="team-info-grid">
        <div class="team-info-item">
          <strong>Device IP:</strong>
          {device.ip || "N/A"}
          {#if device.internal_id}
            <div style="font-size: 0.8em; margin-top: 0.2em;">
              <strong>Internal ID:</strong>
              {device.internal_id}
            </div>
          {/if}
        </div>

        <div class="team-info-item">
          <strong>Team ID:</strong>
          {device.team_id || "N/A"}
          {#if device.team_internal_id}
            <div style="font-size: 0.8em; margin-top: 0.2em;">
              <strong>team Internal ID:</strong>
              {device.team_internal_id}
            </div>
          {/if}
        </div>

        <div class="team-info-item date-group">
          <div>
            <strong>Language:</strong>
            {device.lang || "N/A"}
          </div>
          <div>
            <strong>SIP ID:</strong>
            {device.sip_id || "N/A"}
          </div>
        </div>

        <div class="team-info-item date-group">
          <div>
            <strong>Created At:</strong>
            {formatDate(device.created_at)}
          </div>
          {#if device.last_auth_at}
            <div>
              <strong>Last Active:</strong>
              {timeAgo(device.last_auth_at, "Never")}
            </div>
          {/if}
        </div>
      </div>

      <hr
        style="margin-top: 0.5rem; margin-bottom: 1rem; border-color: #333;"
      />

      <div class="form-container">
        <form
          method="POST"
          action="?/update"
          use:enhance={handleEnhancedSubmit()}
          class="form-section"
        >
          <div class="form-grid">
            <div class="form-group">
              <label for="team_id">Team ID</label>
              <div class="input-container">
                <input
                  type="text"
                  id="team_id"
                  name="team_id"
                  bind:value={teamId}
                  class="form-input"
                  required
                />
              </div>
            </div>

            <div class="form-group">
              <label for="ip">IP Address</label>
              <div class="input-container">
                <input
                  type="text"
                  id="ip"
                  name="ip"
                  bind:value={ip}
                  class="form-input"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="nickname">Nickname</label>
              <div class="input-container">
                <input
                  type="text"
                  id="nickname"
                  name="nickname"
                  bind:value={nickname}
                  class="form-input"
                  placeholder="Optional display name"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="role" class="form-label">Role</label>
              <select
                id="role"
                name="role"
                bind:value={role}
                class="form-input"
              >
                <option value="" disabled={!!role}>Select Role</option>
                <option value="Single">Single</option>
                <option value="TeamLead">Team Lead</option>
                <option value="TeamMember">Team Member</option>
              </select>
            </div>

            <div class="form-group">
              <label for="lang">Language</label>
              <div class="input-container">
                <input
                  type="text"
                  id="lang"
                  name="lang"
                  bind:value={lang}
                  class="form-input"
                  placeholder="e.g., en-US, es-ES, fr-FR"
                />
              </div>
            </div>

            <div class="form-group full-width">
              <div class="config-textarea">
                <label for="vpn_conf"> VPN Configuration </label>
                <div class="input-container">
                  <textarea
                    id="vpn_conf"
                    name="vpn_conf"
                    bind:value={vpnConf}
                    rows={6}
                    class="form-textarea config-textarea"
                    placeholder="Paste VPN configuration here..."
                    spellcheck="false"
                  ></textarea>
                </div>
              </div>
            </div>

            <div class="form-group full-width">
              <div class="config-textarea">
                <label for="msg_conf"> Message Configuration </label>
                <div class="input-container">
                  <textarea
                    id="msg_conf"
                    name="msg_conf"
                    bind:value={msgConf}
                    rows={6}
                    class="form-textarea config-textarea"
                    placeholder="Paste message configuration here..."
                    spellcheck="false"
                  ></textarea>
                </div>
              </div>
            </div>

            <div class="form-group full-width">
              <div class="config-textarea">
                <label for="phone_conf"> Caller configuration </label>
                <div class="input-container">
                  <textarea
                    id="phone_conf"
                    name="phone_conf"
                    bind:value={phoneConf}
                    rows={6}
                    class="form-textarea config-textarea"
                    placeholder="Paste Caller configuration here..."
                    spellcheck="false"
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <button
              class="button button-secondary"
              type="button"
              on:click={() => goto("/admin/devices")}
            >
              Back
            </button>
            <button
              type="submit"
              class="button button-primary"
            >
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  {:else}
    <div class="text-center py-12">
      <p class="text-gray-500">Device not found</p>
    </div>
  {/if}
</div>
